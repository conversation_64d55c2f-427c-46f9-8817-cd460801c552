进入w_test.AllTestCtrl页面粘贴一整页
R完成当前任务
T完成所有成就
Y获取武器，但是输入名字不行，意义不明
J添加随机武器自定义武器和随机装备
L清除前一个武器
I生成武器
O清除背包
N添加当前选择的称号
K全图秒杀
V简陋调用自带后台
U偷存档，排行榜偷存档，需要进入UI.top.TopBoard页面搜索barClick(e:ClickEvent) : void并且覆盖下方所有判定
         var da0:TopBarData = null;
         da0 = e.childData as TopBarData;
         Gaming.testCtrl.uid = da0.uid;
         Gaming.testCtrl.index = da0.index;
         Gaming.uiGroup.alertBox.showSuccess("存档获取成功,按U载入存档");

排行榜偷存档，需要进入UI.arena.top.ArenaTopBoard页面，和上面排行榜偷存档改法一样


package w_test
{
   import UI.creator.arms.ArmsCreatorBox;
   import UI.test.LevelTestCtrl;
   import com.adobe.serialization.json.JSON2;
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.login.SaveData4399;
   import dataAll._data.ConstantDefine;
   import dataAll.arms.save.ArmsSave;
   import dataAll.skill.define.SkillDefine;
   import dataAll.skill.define.otherObj.SkillChangeHurt;
   import flash.display.Graphics;
   import flash.events.KeyboardEvent;
   import flash.system.System;
   import flash.ui.Keyboard;
   import flash.utils.getTimer;
   import gameAll.arms.GameArmsCtrl;
   import gameAll.body.IO_NormalBody;
   import w_test.bug.UplevelBug;
   import w_test.cheating._CheatingController;
   
   public class AllTestCtrl
   {
      public var uid:String = "";
      
      public var index:String = "";
      
      public var isApiB:Boolean = false;
      
      public var isComB:Boolean = false;
      
      public var enabled:Boolean = false;
      
      public var gr:Graphics;
      
      public var drop:TestCtrl_Drop;
      
      public var map:TestCtrl_Map;
      
      public var hit:TestCtrl_Hit;
      
      public var ai:TestCtrl_AI;
      
      public var arms:TestCtrl_Arms;
      
      public var text:TestCtrl_Text;
      
      public var tip:TestCtrl_Tip;
      
      public var say:TestCtrl_Say;
      
      public var skill:TestCtrl_skill;
      
      public var save:TestCtrl_Save;
      
      public var level:TestCtrl_Level;
      
      public var ui:TestCtrl_UI;
      
      public var arr:Array;
      
      public var uplevelBug:UplevelBug;
      
      public var cheating:_CheatingController;
      
      public var levelTest:LevelTestCtrl;
      
      private var armsCreatorBox:ArmsCreatorBox = null;
      
      public function AllTestCtrl()
      {
         this.drop = new TestCtrl_Drop();
         this.map = new TestCtrl_Map();
         this.hit = new TestCtrl_Hit();
         this.ai = new TestCtrl_AI();
         this.arms = new TestCtrl_Arms();
         this.text = new TestCtrl_Text();
         this.tip = new TestCtrl_Tip();
         this.say = new TestCtrl_Say();
         this.skill = new TestCtrl_skill();
         this.save = new TestCtrl_Save();
         this.level = new TestCtrl_Level();
         this.ui = new TestCtrl_UI();
         this.arr = [];
         this.uplevelBug = new UplevelBug();
         this.cheating = new _CheatingController();
         this.levelTest = new LevelTestCtrl();
         super();
         this.arr.push(this.arms);
         this.cheating.fun_arr = ClassProperty.getFunArr(this.cheating);
      }
      
      public function opneOrClose(name0:String) : void
      {
         var obj0:Object = this[name0];
         var i0:int = int(this.arr.indexOf(obj0));
         if(i0 >= 0)
         {
            this.arr.splice(i0,1);
         }
         else
         {
            this.arr.push(i0);
         }
      }
      
      public function afterUIInit(url0:String) : void
      {
         this.gr = Gaming.gameSprite.L_testShape.graphics;
         this.map.gr = this.gr;
         if(Gaming.uiGroup.testUI.haveDataB)
         {
            if(Gaming.api.save.isLocal())
            {
               this.enabled = true;
               this.isComB = false;
               this.isApiB = false;
            }
            else if(url0.indexOf(ConstantDefine.testUrl) >= 0)
            {
               this.enabled = true;
               this.isComB = false;
               this.isApiB = true;
            }
            else
            {
               this.enabled = false;
               this.isComB = true;
               this.isApiB = false;
            }
         }
         Gaming.uiGroup.showStat(false);
      }
      
      public function canCheatingB() : Boolean
      {
         return this.enabled == true || this.isApiB;
      }
      
      public function mouseClickFun(e:*) : void
      {
         this.mouseClick();
      }
      
      public function mouseClick() : void
      {
         var n:* = undefined;
         var d0:TestCtrl_Normal = null;
         for(n in this.arr)
         {
            d0 = this.arr[n];
            d0.mouseClick();
         }
      }
      
      public function keyDown(e:KeyboardEvent) : void
      {
         var name0:String = Gaming.uiGroup.headUI.noBoard.nowChooseName;
         var n:* = undefined;
         var d2:TestCtrl_Normal = null;
         for(n in this.arr)
         {
            d2 = this.arr[n];
            d2.keyDown(e);
         }
         if(e.keyCode != Keyboard.NUMBER_2)
         {
            if(e.keyCode != Keyboard.NUMBER_3)
            {
               if(e.keyCode != Keyboard.NUMBER_4)
               {
                  if(e.keyCode == Keyboard.N)
                  {
                     name0 = Gaming.uiGroup.headUI.noBoard.nowChooseName;
                     if(name0 != "")
                     {
                        Gaming.PG.da.head.addHead(name0,Gaming.api.save.getNowServerDate().getStr());
                        Gaming.uiGroup.alertBox.showSuccess("添加称号：" + name0);
                     }
                  }
                  else if(e.keyCode == Keyboard.T)
                  {
                     Gaming.uiGroup.alertBox.textInput.showTextInput("输入[YES]添加所有勋章","YES",this.completeAllAchieve);
                  }
                  else if(e.keyCode == Keyboard.U)
                  {
                     if(this.uid != "")
                     {
                        Gaming.uiGroup.alertBox.textInput.showTextInput("输入竞技场代码或者[" + ComMethod.color("uid_index","#fd397b") + "]格式数据",this.uid + "_" + this.index,this.sSave);
                     }
                     else
                     {
                        Gaming.uiGroup.alertBox.textInput.showTextInput("输入竞技场代码或者[" + ComMethod.color("uid_index","#fd397b") + "]格式数据","",this.sSave);
                     }
                  }
                  else if(e.keyCode == Keyboard.O)
                  {
                     Gaming.uiGroup.alertBox.textInput.showTextInput("输入[YES]清除当前背包","YES",this.clearBag);
                  }
                  else if(e.keyCode == Keyboard.I)
                  {
                     this.showArmsCreator();
                  }
                  else if(e.keyCode == Keyboard.Y)
                  {
                     Gaming.uiGroup.alertBox.textInput.showTextInput("输入武器代码","",this.addArms);
                  }
                  else if(e.keyCode == Keyboard.R)
                  {
                     Gaming.uiGroup.taskUI.test_nowTaskComplete();
                     Gaming.uiGroup.alertBox.showSuccess("当前接取任务已完成");
                  }
                  else if(e.keyCode == Keyboard.K)
                  {
                     Gaming.TG.hurt.killAllEnemy(null);
                  }
                  else if(e.keyCode == Keyboard.V)
                  {
                     Gaming.uiGroup.alertBox.textInput.showTextInput("请输入代码[f0*name0*str0*v0]\n例:things*addAllThings*5000*5000,是增加全物品5000","",this.gfht);
                  }
                  else if(e.keyCode == Keyboard.M)
                  {
                     Gaming.uiGroup.testUI.showAndHide();
                  }
               }
            }
         }
      }
      
      public function gfht(str0:String) : void
      {
         var Arr_Test:Array = new Array();
         Arr_Test = str0.split("*",str0.length);
         Gaming.testCtrl.cheating.doOrder(String(Arr_Test[0]),String(Arr_Test[1]),String(Arr_Test[2]),Number(Arr_Test[3]));
      }
      
      public function addArms(str0:String) : void
      {
         var obj0:Object = JSON2.decode(str0);
         var s0:ArmsSave = new ArmsSave();
         s0.inData_byObj(obj0);
         Gaming.PG.da.armsBag.addSave(s0);
         Gaming.uiGroup.alertBox.showSuccess("武器添加成功！");
      }
      
      public function sSave(str0:String) : void
      {
         var arearCode:String = "";
         var str2:String = "";
         if(str0.indexOf("_") < 1)
         {
            str0 = TextWay.toHanSpace(TextWay.toHan2(str0));
            arearCode = Base64.decodeString(str0);
            if(arearCode.indexOf("_") < 1 || arearCode.indexOf(",") < 1)
            {
               Gaming.uiGroup.alertBox.showError("竞技场代码格式不正确。");
            }
            else
            {
               str2 = String(arearCode.split(",")[0]);
               this.getUserData(str2.split("_")[0],str2.split("_")[1]);
            }
         }
         else
         {
            this.getUserData(str0.split("_")[0],str0.split("_")[1]);
         }
      }
      
      public function getUserData(uid0:String, index0:int) : void
      {
         Gaming.api.top.getUserData(uid0,index0,this.yes_getSaveByUid,this.no_getSaveByUid);
      }
      
      private function yes_getSaveByUid(ud0:SaveData4399) : void
      {
         var str0:String = JSON2.encode(ud0.data);
         var obj0:Object = JSON2.decode(str0);
         Gaming.uiGroup.loginUI.outLoginEvent();
         Gaming.PG.loginData.newSave(0);
         Gaming.uiGroup.loginUI.yes_read(obj0);
      }
      
      private function no_getSaveByUid(str0:String = "") : void
      {
         Gaming.uiGroup.alertBox.showNormal(str0,"yes",null,null,"no");
      }
      
      public function showArmsCreator() : String
      {
         var box0:ArmsCreatorBox = this.armsCreatorBox;
         if(!box0)
         {
            box0 = new ArmsCreatorBox();
            box0.setImg(Gaming.swfLoaderManager.getResource("ArenaUI","armsCreatorBox"));
            Gaming.gameSprite.L_topUI.addChild(box0);
            box0.x = 70;
            box0.y = 30;
            this.armsCreatorBox = box0;
         }
         box0.addArmsSave(this.yesAddArms);
         return "";
      }
      
      private function yesAddArms(s0:ArmsSave) : void
      {
         Gaming.PG.da.armsBag.addSave(s0);
         GameArmsCtrl.addArmsSaveResoure(s0);
      }
      
      public function clearBag(str0:String) : void
      {
         var label0:String = null;
         if(str0 == "YES")
         {
            label0 = Gaming.uiGroup.bagUI.labelBox.nowLabel;
            if(Gaming.uiGroup.skillUI.visible)
            {
               Gaming.uiGroup.skillUI.wearBox.bagBox.fatherData.clearData();
               Gaming.uiGroup.alertBox.showSuccess("清除了技能背包！");
            }
            if(label0 != "")
            {
               Gaming.PG.da[label0 + "Bag"].clearData();
               Gaming.uiGroup.alertBox.showSuccess("清除了" + label0 + "背包");
            }
         }
      }
      
      public function completeAllAchieve(str0:String) : void
      {
         if(str0 == "YES")
         {
            Gaming.PG.da.achieve.completeAll();
            Gaming.uiGroup.alertBox.showSuccess("添加所有勋章！");
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("输入错误！");
         }
      }
      
      public function test() : void
      {
         var i:int = 0;
         i = 0;
         while(i < 50)
         {
            i++;
         }
      }
      
      public function testTime() : void
      {
         var i:int = 0;
         var s2:StringDate = null;
         var cx0:Number = NaN;
         var s0:StringDate = new StringDate();
         s0.inData_byStr("2014-1-20 18:45:11");
         i = 0;
         while(i < 24)
         {
            s2 = new StringDate();
            s2.inData_byStr("2014-2-19 " + i + ":55:10");
            cx0 = s0.compareDate(s2);
            i++;
         }
      }
      
      private function testEffect() : void
      {
         var x0:int = 0;
         var y0:int = 0;
         var b0:IO_NormalBody = Gaming.BG.WE_ARR[0];
         if(b0)
         {
            x0 = Gaming.gameSprite.L_game.mouseX;
            y0 = Gaming.gameSprite.L_game.mouseY;
            Gaming.EG.special.addStaticLightning(x0,y0,b0.getMot().x,b0.getMot().y,0.1);
         }
      }
      
      public function testObjGetter() : void
      {
         var i:int = 0;
         this.testObjGetter_static();
         var d0:SkillDefine = Gaming.defineGroup.skill.getDefine("Hit_atry_godArmsSkill");
         var tt0:Number = getTimer();
         var mem0:Number = System.totalMemory;
         i = 0;
         while(i < 1000000)
         {
            i++;
         }
         trace("testObjGetter耗时：" + (getTimer() - tt0) + "   内存提升：" + (System.totalMemory - mem0));
      }
      
      public function testObjGetter_static() : void
      {
         var i:int = 0;
         var d0:SkillDefine = Gaming.defineGroup.skill.getDefine("Hit_atry_godArmsSkill");
         var tt0:Number = getTimer();
         var mem0:Number = System.totalMemory;
         i = 0;
         while(i < 1000000)
         {
            if("" != SkillChangeHurt.lifePerLess)
            {
               if("" != SkillChangeHurt.player)
               {
                  if("" != SkillChangeHurt.lifePerMore)
                  {
                     if("" != SkillChangeHurt.onlyAttack)
                     {
                        if("" != SkillChangeHurt.onlyWeapon)
                        {
                           if("" != SkillChangeHurt.accurate)
                           {
                              if("" != SkillChangeHurt.hyperopia)
                              {
                                 if("" != SkillChangeHurt.myopia)
                                 {
                                    if("" != SkillChangeHurt.backWeak)
                                    {
                                       if("" != SkillChangeHurt.atry)
                                       {
                                          if("" != SkillChangeHurt.despise)
                                          {
                                             if("" != SkillChangeHurt.killCharm)
                                             {
                                                if("" != SkillChangeHurt.defenceBounceAndImploding)
                                                {
                                                   if("" != SkillChangeHurt.likeMissle)
                                                   {
                                                      if("" != SkillChangeHurt.followBullet)
                                                      {
                                                         if("" != SkillChangeHurt.enemyType)
                                                         {
                                                            if("" != SkillChangeHurt.cmldef)
                                                            {
                                                               if("" != SkillChangeHurt.vehicle)
                                                               {
                                                                  if("" == SkillChangeHurt.stationaryBoss)
                                                                  {
                                                                  }
                                                               }
                                                            }
                                                         }
                                                      }
                                                   }
                                                }
                                             }
                                          }
                                       }
                                    }
                                 }
                              }
                           }
                        }
                     }
                  }
               }
            }
            i++;
         }
         trace("testObjGetter_static耗时：" + (getTimer() - tt0) + "   内存提升：" + (System.totalMemory - mem0));
      }
      
      private function testObjGetter2() : void
      {
         var i:int = 0;
         var d0:SkillDefine = Gaming.defineGroup.skill.getDefine("Hit_atry_godArmsSkill");
         var tt0:Number = getTimer();
         var mem0:Number = System.totalMemory;
         i = 0;
         while(i < 1000000)
         {
            switch("")
            {
               case "lifePerLess":
                  break;
               case "player":
                  break;
               case "lifePerMore":
                  break;
               case "onlyAttack":
                  break;
               case "onlyWeapon":
                  break;
               case "accurate":
                  break;
               case "hyperopia":
                  break;
               case "myopia":
                  break;
               case "backWeak":
                  break;
               case "atry":
                  break;
               case "despise":
                  break;
               case "killCharm":
                  break;
               case "defenceBounceAndImploding":
                  break;
               case "likeMissle":
                  break;
               case "followBullet":
                  break;
               case "enemyType":
                  break;
               case "cmldef":
                  break;
               case "vehicle":
                  break;
               case "stationaryBoss":
                  break;
            }
            i++;
         }
         trace("testObjGetter switch耗时：" + (getTimer() - tt0) + "   内存提升：" + (System.totalMemory - mem0));
      }
      
      public function FTimer() : void
      {
         var n:* = undefined;
         var d0:TestCtrl_Normal = null;
         if(!this.cheating.enabled)
         {
            return;
         }
         this.levelTest.FTimer2();
         if(!this.enabled)
         {
            return;
         }
         for(n in this.arr)
         {
            d0 = this.arr[n];
            d0.FTimer();
         }
      }
   }
}

