进入UI.top.TopBoard页面覆盖一整页


package UI.top
{
   import UI.NormalUICtrl;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.page.PageBox;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.top.TopBarData;
   import dataAll._app.top.TopBarDataGroup;
   import dataAll._app.top.define.TopBarDefineGroup;
   import dataAll._app.top.player.PlayerTopReturnData;
   import dataAll._app.top.player.PlayerTopUploadData;
   import dataAll._player.PlayerData;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class TopBoard extends NormalUI
   {
      private var box:TopBarBox;
      
      private var topTxt:TextField;
      
      private var scoreTxt:TextField;
      
      private var imgBox:TopHeroImageBox;
      
      private var pageBox:PageBox;
      
      private var imgSp:Sprite;
      
      private var barTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var playerTag:Sprite;
      
      private var armsTag:Sprite;
      
      private var nowDefineG:TopBarDefineGroup;
      
      private var maxBarNum:int = 10;
      
      private var nowPage:int = 0;
      
      public function TopBoard()
      {
         this.box = new TopBarBox();
         this.imgBox = new TopHeroImageBox();
         this.pageBox = new PageBox();
         super();
      }
      
      private function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["imgSp","topTxt","scoreTxt","barTag","pageTag","playerTag","armsTag"];
         super.setImg(img0);
         this.box.initTitle("TopUI/topBar",3);
         this.box.arg.init(1,10,0,1);
         this.box.evt.setWantEvent(true,false,false,true,true);
         this.box.x = this.barTag.x;
         this.box.y = this.barTag.y;
         this.box.addEventListener(ClickEvent.ON_CLICK,this.barClick);
         this.box.addEventListener(ClickEvent.ON_OVER,this.barOver);
         this.box.addEventListener(ClickEvent.ON_OUT,this.barOut);
         addChild(this.box);
         this.pageBox.setToNormalBtn();
         addChild(this.pageBox);
         NormalUICtrl.setTag(this.pageBox,this.pageTag);
         this.pageBox.setMaxPageShow(10);
         this.pageBox.setPageNumOut(10);
         this.pageBox.addEventListener(ClickEvent.ON_SHOW_PAGE,this.pageClick);
         if(this.imgSp)
         {
            addChild(this.imgBox);
            this.imgBox.setImg(this.imgSp);
            this.imgBox.visible = false;
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function stopAllCartoon() : void
      {
      }
      
      private function barClick(e:ClickEvent) : void
      {
         var da0:TopBarData = null;
         da0 = e.childData as TopBarData;
         Gaming.testCtrl.uid = da0.uid;
         Gaming.testCtrl.index = da0.index;
         Gaming.uiGroup.alertBox.showSuccess("获取成功,按U载入");
      }
      
      private function barOver(e:ClickEvent) : void
      {
         var da0:TopBarData = e.childData as TopBarData;
         var dg0:TopBarDefineGroup = da0.define;
         if(!da0.hideTextB())
         {
            if(dg0.objType == "dps" && dg0.gather != "pet" && dg0.gather != "more")
            {
               this.imgBox.showByBar(e.child as TopBar);
            }
         }
      }
      
      private function barOut(e:ClickEvent) : void
      {
         this.imgBox.visible = false;
      }
      
      private function oneDpsShow(da0:TopBarData) : void
      {
      }
      
      public function showPage(num0:int) : void
      {
         this.pageBox.showPage(num0);
      }
      
      private function pageClick(e:ClickEvent) : void
      {
         this.getRankList(e.index);
      }
      
      public function start(type0:String) : void
      {
         this.nowPage = 0;
         this.nowDefineG = Gaming.defineGroup.top.getDefine(type0);
         this.uploadScore();
      }
      
      private function uploadScore() : void
      {
         var noStr0:String = null;
         Gaming.uiGroup.connectUI.show("上传成绩……");
         var da0:PlayerTopUploadData = this.PD.getTopUploadData(this.nowDefineG);
         if(da0.score == 0)
         {
            this.no_unloadScore("");
         }
         else
         {
            noStr0 = this.PD.union.topPan();
            if(noStr0 == "" && this.PD.union.isInUnionB())
            {
               Gaming.api.top.submitScore(da0,this.yes_unloadScore,this.no_unloadScore);
            }
            else
            {
               this.no_unloadScore(noStr0);
            }
         }
      }
      
      private function yes_unloadScore(returnDataObj:Object) : void
      {
         var da0:PlayerTopReturnData = new PlayerTopReturnData();
         da0.inData_byObj(returnDataObj);
         this.topTxt.htmlText = "排名:" + ComMethod.color(da0.curRank + "","#00FFFF",30);
         this.scoreTxt.htmlText = "分数:" + ComMethod.color(NumberMethod.toBigWan(da0.curScore),"#FF9900",30);
         this.setBeforeText(da0.lastRank + "",da0.lastScore + "");
         this.showPage(this.nowPage);
         Gaming.PG.save.headCount.inTop(this.nowDefineG.name,da0.curRank);
      }
      
      private function no_unloadScore(str0:String = "") : void
      {
         if(str0 == "")
         {
            str0 = "无排名";
         }
         this.topTxt.htmlText = str0;
         this.scoreTxt.htmlText = "";
         this.setBeforeText("无","无");
         this.showPage(this.nowPage);
      }
      
      private function setBeforeText(rank0:String, score0:String) : void
      {
      }
      
      private function getRankList(page0:int) : void
      {
         Gaming.uiGroup.connectUI.show("获取排行榜数据……");
         Gaming.api.top.getRankListsData(this.nowDefineG.id,this.maxBarNum,page0 + 1,this.yes_getRankList,this.no_getRankList);
      }
      
      private function yes_getRankList(dataArr0:Array) : void
      {
         var dg0:TopBarDataGroup = new TopBarDataGroup();
         dg0.inData_byArr(dataArr0,this.nowDefineG.name);
         this.box.inData_byTop(dg0);
         Gaming.uiGroup.connectUI.hide();
      }
      
      private function no_getRankList(str0:String = "") : void
      {
         Gaming.uiGroup.alertBox.showNormal("获取排行榜数据失败！\n" + str0,"yes",null,null,"no");
         Gaming.uiGroup.connectUI.hide();
      }
      
      public function FTimer() : void
      {
         if(visible)
         {
            this.imgBox.FTimer();
         }
      }
   }
}

