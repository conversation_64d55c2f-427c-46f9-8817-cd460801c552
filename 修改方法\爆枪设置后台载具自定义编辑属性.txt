进入UI.vehicle.VehicleEvolutionBoard页面，并且覆盖一整页

package UI.vehicle
{
   import UI.bag.ItemsGrid;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.must.NormalMustBox;
   import dataAll.equip.vehicle.VehicleData;
   import dataAll.equip.vehicle.VehicleDataCreator;
   import dataAll.equip.vehicle.VehicleSave;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class VehicleEvolutionBoard extends NormalUI
   {
       
      
      public var vehicleUI:UI.vehicle.VehicleUI;
      
      private var coverSp:Sprite;
      
      private var mustTag:Sprite;
      
      private var btnSp:MovieClip;
      
      private var beforeSp:Sprite;
      
      private var afterSp:Sprite;
      
      private var beforeBox:UI.vehicle.VehicleUpgradeOneBox;
      
      private var afterBox:UI.vehicle.VehicleUpgradeOneBox;
      
      private var mustBox:NormalMustBox;
      
      private var btn:NormalBtn;
      
      private var nowData:VehicleData;
      
      public function VehicleEvolutionBoard()
      {
         this.beforeBox = new UI.vehicle.VehicleUpgradeOneBox();
         this.afterBox = new UI.vehicle.VehicleUpgradeOneBox();
         this.mustBox = new NormalMustBox();
         this.btn = new NormalBtn();
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["coverSp","mustTag","btnSp","beforeSp","afterSp"];
         super.setImg(img0);
         addChild(this.beforeBox);
         this.beforeBox.setImg(this.beforeSp);
         addChild(this.afterBox);
         this.afterBox.setImg(this.afterSp);
         addChild(this.mustBox);
         this.mustBox.setNormalImg();
         this.mustBox.x = this.mustTag.x;
         this.mustBox.y = this.mustTag.y;
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.btn.setName("属性编辑");
         this.btn.actived = true;
         this.setCoverText("");
         addChild(this.coverSp);
         this.clearMust();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      public function outLoginEvent() : void
      {
         this.nowData = null;
      }
      
      protected function setCoverText(str0:String) : void
      {
         if(str0 == "")
         {
            this.coverSp.visible = false;
         }
         else
         {
            this.coverSp.visible = true;
            this.coverSp["txt"].text = str0;
         }
      }
      
      private function fleshData() : void
      {
         this.showData(this.nowData);
      }
      
      public function setNowData(da0:VehicleData) : void
      {
         this.nowData = da0;
      }
      
      public function gripClick(e:ClickEvent) : void
      {
         if(visible)
         {
            this.showData(e.childData as VehicleData);
         }
      }
      
      private function showData(da0:VehicleData) : void
      {
         var afterDa0:VehicleData = null;
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         this.nowData = da0;
         var grip0:ItemsGrid = this.vehicleUI.itemsBox.findGripByData(this.nowData);
         if(grip0 is ItemsGrid)
         {
            this.setCoverText("");
            this.vehicleUI.itemsBox.setChoose_byIndex(grip0.index);
            afterDa0 = da0.getEvolutionData();
            this.beforeBox.inEvoData(da0,"进化前：");
            this.afterBox.inEvoData(afterDa0,"进化后：");
            if(afterDa0)
            {
               must_d0 = VehicleDataCreator.getEvolutionMust(da0);
               bb0 = this.mustBox.inData(must_d0,da0.normalPlayerData.level);
               this.btn.actived = true;
            }
            else
            {
               this.clearMust();
            }
         }
         else
         {
            this.setCoverText("请在右边选择你要编辑属性的载具");
            this.vehicleUI.itemsBox.setChoose_byIndex(-1);
            this.clearMust();
         }
      }
      
      private function clearMust() : void
      {
         this.btn.actived = true;
         this.mustBox.setShowState(false);
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         if(this.nowData)
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("碾压攻击[00*数量]  机枪攻击[01*数量]\n主炮攻击[02*数量]  耐久系数[03*数量]\n进化等级[04*数量]  载具等级[05*数量]","",this.VehicleEdit);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("载具数据不存在！");
         }
      }
      
      private function VehicleEdit(str0:String) : void
      {
         var s0:VehicleSave = new VehicleSave();
         var ArrNum:Array = new Array();
         var ArrNow:Array = new Array();
         var VehicleNow:String = null;
         ArrNum = str0.split("&",str0.length);
         s0.inData_byObj(this.nowData.save);
         for each(VehicleNow in ArrNum)
         {
            ArrNow = VehicleNow.split("*",VehicleNow.length);
            if(ArrNow[0] == "00")
            {
               s0.attackMulAddLv = Number(Number(Number(Number(ArrNow[1]))));
            }
            else if(ArrNow[0] == "01")
            {
               s0.subMulAddLv = Number(Number(Number(Number(ArrNow[1]))));
            }
            else if(ArrNow[0] == "02")
            {
               s0.mainMulAddLv = Number(Number(Number(Number(ArrNow[1]))));
            }
            else if(ArrNow[0] == "03")
            {
               s0.lifeMulAddLv = Number(Number(Number(Number(ArrNow[1]))));
            }

            else if(ArrNow[0] == "04")
            {
               s0.evoLv = Number(Number(Number(Number(ArrNow[1]))));
            }
            else if(ArrNow[0] == "05")
            {
               s0.itemsLevel = Number(Number(Number(Number(ArrNow[1]))));
            }
         }
         this.nowData.save = s0;
         this.vehicleUI.fleshBag();
         this.fleshData();
         Gaming.uiGroup.alertBox.showSuccess("载具属性编辑成功！");
      }
      
      private function affterBtnClick() : void
      {
         var da0:VehicleData = this.nowData;
         var afterDa0:VehicleData = da0.getEvolutionData();
         da0.changeToOneData(afterDa0);
         Gaming.uiGroup.alertBox.showSuccess("进化成功！");
         Gaming.PG.save.headCount.vehicleEvoNum++;
         this.vehicleUI.fleshBag();
         this.fleshData();
      }
   }
}
